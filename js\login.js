document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已登录并在登录页面
    const userInfo = localStorage.getItem('userInfo');
    const loginDate =userInfo ? JSON.parse(userInfo).loginDate : null;
    if (userInfo && loginDate === new Date().toISOString().split('T')[0] && window.location.href.includes('login.html')) {
        window.location.href = 'index.html';
        return;
    }
    
    const loginForm = document.querySelector('.login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const account = document.getElementById('account').value;
            const password = document.getElementById('password').value;
            
            fetch('php/login.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    account: account,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 登录成功，存储用户信息和登录日期
                    const userInfo = {...data.data,loginDate: new Date().toISOString().split('T')[0]};
                    localStorage.setItem('userInfo', JSON.stringify(userInfo));
                    
                    // 跳转到主页
                    window.location.href = data.redirect || 'index.html';
                } else {
                    alert(data.message || '登录失败！');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('登录请求失败，请稍后重试');
            });
        });
    }
}); 
