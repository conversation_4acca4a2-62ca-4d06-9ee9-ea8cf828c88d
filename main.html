<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备综合管理系统 - Ant Design版</title>
    <link rel="icon" href="pic/icon/weblogo1.png">

    <!-- Ant Design 样式文件 -->
    <link rel="stylesheet" href="lib/antd/reset.min.css">

    <!-- 自定义样式 -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        #root {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .app-header {
            background: white;
            color: #333;
            padding: 0 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 64px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-bottom: 1px solid #f0f0f0;
            z-index: 1000;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 6px;
            transition: background-color 0.3s;
            position: relative;
        }

        .user-profile:hover {
            background-color: rgba(0, 0, 0, 0.04);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #1890ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .app-layout {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .app-sider {
            background: white;
            overflow-y: auto;
            height: calc(100vh - 64px);
            position: relative;
            width: 256px;
            flex-shrink: 0;
            border-right: 1px solid #f0f0f0;
        }

        .app-content {
            flex: 1;
            background: #f0f2f5;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .content-header {
            background: white;
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .content-body {
            flex: 1;
            padding: 0;
            overflow: hidden;
        }

        #tabContainer {
            height: 100%;
        }

        .ant-tabs {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .ant-tabs-content-holder {
            flex: 1;
            overflow: hidden;
        }

        .ant-tabs-tabpane {
            height: 100%;
            overflow-y: auto;
            padding: 24px;
        }

        .welcome-content {
            background: white;
            border-radius: 8px;
            padding: 32px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin: 0;
        }

        /* 标签页样式优化 */
        .ant-tabs-tab {
            border-radius: 6px 6px 0 0 !important;
        }

        .ant-tabs-tab-active {
            background: white !important;
        }

        .ant-tabs-content {
            background: #f0f2f5;
        }



        .menu-trigger {
            display: none;
            color: #333;
            font-size: 18px;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .menu-trigger:hover {
            background-color: rgba(0, 0, 0, 0.04);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .menu-trigger {
                display: block;
            }

            .header-title {
                font-size: 16px;
            }

            .app-sider {
                position: fixed;
                left: -256px;
                top: 64px;
                z-index: 999;
                transition: left 0.3s ease;
                box-shadow: 2px 0 8px rgba(0,0,0,0.15);
            }

            .app-sider.mobile-open {
                left: 0;
            }

            .app-content {
                margin-left: 0;
            }

            .mobile-overlay {
                position: fixed;
                top: 64px;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.45);
                z-index: 998;
                display: none;
            }

            .mobile-overlay.show {
                display: block;
            }

            .content-body {
                padding: 16px;
            }

            .welcome-content {
                padding: 24px 16px;
            }
        }

        @media (max-width: 480px) {
            .header-left img {
                width: 80px;
                height: 17px;
            }

            .header-title {
                font-size: 14px;
            }

            .content-body {
                padding: 12px;
            }

            .welcome-content {
                padding: 20px 12px;
            }
        }

        /* 自定义菜单样式 */
        .ant-menu {
            background: transparent;
            border-right: none;
        }

        .ant-menu .ant-menu-item {
            color: #333;
            margin: 4px 8px;
            border-radius: 6px;
            transition: all 0.3s;
        }

        .ant-menu .ant-menu-item:hover {
            color: #333;
            background-color: #f5f5f5;
        }

        .ant-menu .ant-menu-item-selected {
            background-color: #e6f7ff;
            color: #1890ff;
        }

        .ant-menu .ant-menu-submenu-title {
            color: #333;
            margin: 4px 8px;
            border-radius: 6px;
            transition: all 0.3s;
        }

        .ant-menu .ant-menu-submenu-title:hover {
            color: #1890ff;
            background-color: #f5f5f5;
        }

        .ant-menu .ant-menu-submenu-open > .ant-menu-submenu-title {
            color: #1890ff;
        }

        .ant-menu .ant-menu-sub .ant-menu-item {
            color: #666;
        }

        .ant-menu .ant-menu-sub .ant-menu-item:hover {
            color: #333;
            background-color: #f5f5f5;
        }

        .ant-menu .ant-menu-sub .ant-menu-item-selected {
            background-color: #e6f7ff;
            color: #1890ff;
        }

        .menu-icon {
            margin-right: 8px;
            font-size: 16px;
        }

        /* 用户菜单样式 */
        .user-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            min-width: 120px;
            z-index: 1001;
        }

        .menu-item {
            padding: 8px 16px;
            cursor: pointer;
            color: #333;
            transition: background-color 0.3s;
        }

        .menu-item:hover {
            background-color: #f5f5f5;
        }

        .menu-item:first-child {
            border-radius: 6px 6px 0 0;
        }

        .menu-item:last-child {
            border-radius: 0 0 6px 6px;
        }
    </style>
</head>
<body>
    <div id="root">
        <!-- 顶部导航栏 -->
        <div class="app-header">
            <div class="header-left">
                <div class="menu-trigger" id="menuTrigger">☰</div>
                <img src="./pic/01_TCL 华星_Logo_RGB_标准.png" width="120" height="25" alt="TCL华星">
                <span class="header-title">设备综合管理系统丨EQCM</span>
            </div>
            <div class="header-right">
                <span style="font-size: 18px;">✉</span>
                <div class="user-profile" id="userProfile">
                    <div class="user-avatar" id="avatarText">U</div>
                    <span id="headerUsername">用户</span>
                </div>
            </div>
        </div>

        <!-- 主体布局 -->
        <div class="app-layout">
            <!-- 侧边栏 -->
            <div class="app-sider" id="appSider">
                <div id="sideMenu"></div>
            </div>

            <!-- 内容区域 -->
            <div class="app-content">
                <div class="content-body">
                    <div id="tabContainer"></div>
                </div>
            </div>
        </div>

        <!-- 移动端遮罩层 -->
        <div class="mobile-overlay" id="mobileOverlay"></div>
    </div>

    <!-- 引入依赖库 -->
    <script src="lib/react.production.min.js"></script>
    <script src="lib/react-dom.production.min.js"></script>
    <script src="lib/lodash.min.js"></script>
    <script src="lib/dayjs.min.js"></script>
    <script src="lib/antd/antd.min.js"></script>

    <script>
        // 解构 Ant Design 组件
        const { Menu, Layout, Button, Space, ConfigProvider, Tabs } = antd;
        const { createElement: h, useState, useRef } = React;

        // 中文国际化配置
        const zhCN = { locale: 'zh-CN' };

        // 菜单数据配置
        const menuItems = [
            {
                key: 'home',
                icon: '🏠',
                label: '首页',
                onClick: () => switchToTab('home')
            },
            {
                key: 'associate',
                icon: '📋',
                label: '交接管理',
                children: [
                    {
                        key: 'associate-search',
                        label: '交接查询',
                        onClick: () => addTab('associate-search', '交接查询', '交接查询功能页面')
                    },
                    {
                        key: 'associate-register',
                        label: '交接登录',
                        onClick: () => addTab('associate-register', '交接登录', '交接登录功能页面')
                    },
                    {
                        key: 'associate-analysis',
                        label: '交接分析',
                        onClick: () => addTab('associate-analysis', '交接分析', '交接分析功能页面')
                    }
                ]
            },
            {
                key: 'fault',
                icon: '🧰',
                label: '大故障',
                children: [
                    {
                        key: 'fault-analysis',
                        label: '故障分析',
                        onClick: () => addTab('fault-analysis', '故障分析', '故障分析功能页面')
                    },
                    {
                        key: 'fault-list',
                        label: '故障列表',
                        onClick: () => addTab('fault-list', '故障列表', '故障列表功能页面')
                    }
                ]
            },
            {
                key: 'site',
                icon: '📡',
                label: '现场管理',
                children: [
                    {
                        key: 'site-management',
                        label: '现场管理',
                        onClick: () => addTab('site-management', '现场管理', '现场管理功能页面')
                    },
                    {
                        key: 'tact-time',
                        label: 'TactTime',
                        onClick: () => addTab('tact-time', 'TactTime', 'TactTime功能页面')
                    },
                    {
                        key: 'kpi',
                        label: '领班业绩',
                        onClick: () => addTab('kpi', '领班业绩', '领班业绩功能页面')
                    }
                ]
            },
            {
                key: 'spareparts',
                icon: '🔨',
                label: '备品管理',
                children: [
                    {
                        key: 'spareparts-list',
                        label: '备品列表',
                        onClick: () => addTab('spareparts-list', '备品列表', '备品列表功能页面')
                    },
                    {
                        key: 'spareparts-need',
                        label: '备品请购',
                        onClick: () => addTab('spareparts-need', '备品请购', '备品请购功能页面')
                    },
                    {
                        key: 'spareparts-load',
                        label: '备品录入',
                        onClick: () => addTab('spareparts-load', '备品录入', '备品录入功能页面'),
                        adminOnly: true
                    },
                    {
                        key: 'spareparts-history',
                        label: '备品履历',
                        onClick: () => addTab('spareparts-history', '备品履历', '备品履历功能页面'),
                        adminOnly: true
                    }
                ]
            },
            {
                key: 'bom',
                icon: '📚',
                label: '部件BOM',
                children: [
                    {
                        key: 'bom-search',
                        label: 'BOM查询',
                        onClick: () => addTab('bom-search', 'BOM查询', 'BOM查询功能页面')
                    },
                    {
                        key: 'bom-register',
                        label: 'BOM登录',
                        onClick: () => addTab('bom-register', 'BOM登录', 'BOM登录功能页面')
                    }
                ]
            },
            {
                key: 'modelchange',
                icon: '📊',
                label: '切机管理',
                children: [
                    {
                        key: 'modelchange-jig',
                        label: '金型JIG',
                        onClick: () => addTab('modelchange-jig', '金型JIG', '金型JIG功能页面')
                    },
                    {
                        key: 'modelchange-query',
                        label: '切机查询',
                        onClick: () => addTab('modelchange-query', '切机查询', '切机查询功能页面')
                    }
                ]
            },
            {
                key: 'pm',
                icon: '🔧',
                label: 'PM管理',
                children: [
                    {
                        key: 'pm-search',
                        label: 'PM查询',
                        onClick: () => addTab('pm-search', 'PM查询', 'PM查询功能页面')
                    },
                    {
                        key: 'pm-register',
                        label: 'PM登录',
                        onClick: () => addTab('pm-register', 'PM登录', 'PM登录功能页面')
                    }
                ]
            },
            {
                key: 'battery',
                icon: '🔋',
                label: '电池管理',
                children: [
                    {
                        key: 'battery-changelist',
                        label: '电池更换履历',
                        onClick: () => addTab('battery-changelist', '电池更换履历', '电池更换履历功能页面')
                    },
                    {
                        key: 'battery-changein',
                        label: '电池更换登录',
                        onClick: () => addTab('battery-changein', '电池更换登录', '电池更换登录功能页面')
                    },
                    {
                        key: 'battery-info',
                        label: 'AGV电池信息',
                        onClick: () => addTab('battery-info', 'AGV电池信息', 'AGV电池信息功能页面')
                    }
                ]
            },
            {
                key: 'teaching',
                icon: '📑',
                label: '资料手册',
                children: [
                    {
                        key: 'teaching-manual',
                        label: '资料手册',
                        onClick: () => addTab('teaching-manual', '资料手册', '资料手册功能页面')
                    },
                    {
                        key: 'teaching-upload',
                        label: '资料上传',
                        onClick: () => addTab('teaching-upload', '资料上传', '资料上传功能页面')
                    }
                ]
            },
            {
                key: 'notice',
                icon: '📢',
                label: '通知发布',
                children: [
                    {
                        key: 'notice-publish',
                        label: '发布通知',
                        onClick: () => addTab('notice-publish', '发布通知', '发布通知功能页面')
                    },
                    {
                        key: 'notice-history',
                        label: '历史记录',
                        onClick: () => addTab('notice-history', '历史记录', '历史记录功能页面')
                    }
                ]
            },
            {
                key: 'monitoring',
                icon: '🔬',
                label: '参数监控',
                children: [
                    {
                        key: 'monitoring-realtime',
                        label: '实时监控',
                        onClick: () => addTab('monitoring-realtime', '实时监控', '实时监控功能页面')
                    },
                    {
                        key: 'monitoring-management',
                        label: '基准管理',
                        onClick: () => addTab('monitoring-management', '基准管理', '基准管理功能页面')
                    }
                ]
            },
            {
                key: 'ai',
                icon: '🤖',
                label: 'AI小助理',
                onClick: () => addTab('ai', 'AI小助理', 'AI小助理功能页面')
            }
        ];

        // 状态管理
        let currentSelectedKey = 'home';
        let collapsed = false;
        let userInfo = {};
        let tabItems = [
            {
                key: 'home',
                label: '首页',
                children: h('div', { className: 'welcome-content' },
                    h('h1', null, '欢迎使用设备综合管理系统'),
                    h('p', { style: { marginTop: '16px', color: '#666' } }, '请从左侧菜单选择功能模块')
                )
            }
        ];
        let activeTabKey = 'home';
        let newTabIndex = 0;

        // 标签页功能函数
        function addTab(key, label, content) {
            // 检查标签页是否已存在
            const existingTab = tabItems.find(item => item.key === key);
            if (existingTab) {
                // 如果已存在，切换到该标签页
                switchToTab(key);
                return;
            }

            // 创建新标签页
            const newTab = {
                key: key,
                label: label,
                children: h('div', { className: 'welcome-content' },
                    h('h1', null, label),
                    h('p', { style: { marginTop: '16px', color: '#666' } }, content),
                    h('p', { style: { marginTop: '8px', color: '#999', fontSize: '14px' } }, '此功能正在开发中...')
                )
            };

            tabItems.push(newTab);
            activeTabKey = key;
            currentSelectedKey = key;

            renderTabs();

            // 移动端自动关闭菜单
            if (window.innerWidth <= 768) {
                closeMobileMenu();
            }
        }

        function switchToTab(key) {
            activeTabKey = key;
            currentSelectedKey = key;
            renderTabs();

            // 移动端自动关闭菜单
            if (window.innerWidth <= 768) {
                closeMobileMenu();
            }
        }

        function removeTab(targetKey) {
            if (tabItems.length === 1) {
                return; // 至少保留一个标签页
            }

            const targetIndex = tabItems.findIndex(item => item.key === targetKey);
            const newTabs = tabItems.filter(item => item.key !== targetKey);

            if (newTabs.length && targetKey === activeTabKey) {
                const newActiveKey = newTabs[targetIndex === newTabs.length ? targetIndex - 1 : targetIndex].key;
                activeTabKey = newActiveKey;
                currentSelectedKey = newActiveKey;
            }

            tabItems = newTabs;
            renderTabs();
        }

        function onTabEdit(targetKey, action) {
            if (action === 'remove') {
                removeTab(targetKey);
            }
        }

        function onTabChange(key) {
            switchToTab(key);
        }

        // 菜单点击处理
        function handleMenuClick(e) {
            const item = findMenuItem(menuItems, e.key);
            if (item && item.onClick) {
                currentSelectedKey = e.key;
                item.onClick();
            }
        }

        // 查找菜单项
        function findMenuItem(items, key) {
            for (const item of items) {
                if (item.key === key) {
                    return item;
                }
                if (item.children) {
                    const found = findMenuItem(item.children, key);
                    if (found) return found;
                }
            }
            return null;
        }

        // 过滤菜单项（权限控制）
        function filterMenuItems(items) {
            return items.map(item => {
                if (item.children) {
                    const filteredChildren = item.children.filter(child => {
                        if (child.adminOnly && userInfo.splevel !== 1) {
                            return false;
                        }
                        return true;
                    });
                    return { ...item, children: filteredChildren };
                }
                return item;
            }).filter(item => {
                if (item.adminOnly && userInfo.splevel !== 1) {
                    return false;
                }
                return true;
            });
        }

        // 转换菜单项为Ant Design格式
        function convertToAntdMenuItems(items) {
            return items.map(item => {
                const menuItem = {
                    key: item.key,
                    label: item.icon ?
                        h('span', null,
                            h('span', { className: 'menu-icon' }, item.icon),
                            item.label
                        ) : item.label
                };

                if (item.children && item.children.length > 0) {
                    menuItem.children = convertToAntdMenuItems(item.children);
                } else if (item.onClick) {
                    menuItem.onClick = item.onClick;
                }

                return menuItem;
            });
        }

        // 创建菜单组件
        function createMenu() {
            const filteredItems = filterMenuItems(menuItems);
            const antdMenuItems = convertToAntdMenuItems(filteredItems);

            return h(ConfigProvider, { locale: zhCN },
                h(Menu, {
                    theme: 'light',
                    mode: 'inline',
                    selectedKeys: [currentSelectedKey],
                    defaultOpenKeys: ['associate', 'fault', 'spareparts'],
                    inlineCollapsed: collapsed,
                    items: antdMenuItems,
                    onClick: handleMenuClick,
                    style: {
                        height: '100%',
                        borderRight: 0,
                        paddingTop: '16px'
                    }
                })
            );
        }

        // 渲染菜单
        function renderMenu() {
            const menuContainer = document.getElementById('sideMenu');
            ReactDOM.render(createMenu(), menuContainer);
        }

        // 创建标签页组件
        function createTabs() {
            return h(ConfigProvider, { locale: zhCN },
                h(Tabs, {
                    type: 'editable-card',
                    activeKey: activeTabKey,
                    onChange: onTabChange,
                    onEdit: onTabEdit,
                    hideAdd: true,
                    items: tabItems,
                    style: { height: '100%' }
                })
            );
        }

        // 渲染标签页
        function renderTabs() {
            const tabContainer = document.getElementById('tabContainer');
            ReactDOM.render(createTabs(), tabContainer);
        }

        // 移动端菜单控制
        function toggleMobileMenu() {
            const sider = document.getElementById('appSider');
            const overlay = document.getElementById('mobileOverlay');

            if (sider.classList.contains('mobile-open')) {
                closeMobileMenu();
            } else {
                sider.classList.add('mobile-open');
                overlay.classList.add('show');
            }
        }

        function closeMobileMenu() {
            const sider = document.getElementById('appSider');
            const overlay = document.getElementById('mobileOverlay');

            sider.classList.remove('mobile-open');
            overlay.classList.remove('show');
        }

        // 用户信息管理
        function initUserInfo() {
            // 从localStorage获取用户信息
            const storedUserInfo = localStorage.getItem('userInfo');
            if (storedUserInfo) {
                userInfo = JSON.parse(storedUserInfo);
            } else {
                // 默认用户信息
                userInfo = {
                    name: '演示用户',
                    account: 'demo',
                    department: '设备部',
                    section: '维修组',
                    level: 21,
                    splevel: 1
                };
            }

            // 更新UI
            const username = userInfo.name || userInfo.username || '用户';
            document.getElementById('headerUsername').textContent = username;
            document.getElementById('avatarText').textContent = username.charAt(0).toUpperCase();
        }

        // 用户菜单
        function showUserMenu() {
            const userProfile = document.getElementById('userProfile');

            // 移除现有菜单
            const existingMenu = document.querySelector('.user-menu');
            if (existingMenu) {
                existingMenu.remove();
                return;
            }

            const menu = document.createElement('div');
            menu.className = 'user-menu';
            menu.innerHTML = `
                <div class="menu-item" onclick="showUserInfo()">个人信息</div>
                <div class="menu-item" onclick="logout()">退出登录</div>
            `;

            userProfile.appendChild(menu);

            // 点击其他地方关闭菜单
            setTimeout(() => {
                document.addEventListener('click', function closeMenu(e) {
                    if (!userProfile.contains(e.target)) {
                        menu.remove();
                        document.removeEventListener('click', closeMenu);
                    }
                });
            }, 0);
        }

        // 显示用户信息弹窗
        function showUserInfo() {
            const modal = document.createElement('div');
            modal.className = 'user-info-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.45);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1002;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 8px;
                    padding: 24px;
                    min-width: 400px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                ">
                    <div style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 20px;
                        border-bottom: 1px solid #f0f0f0;
                        padding-bottom: 16px;
                    ">
                        <h2 style="margin: 0; color: #333;">个人信息</h2>
                        <span onclick="closeUserInfo()" style="
                            cursor: pointer;
                            font-size: 20px;
                            color: #999;
                            width: 24px;
                            height: 24px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 4px;
                        ">&times;</span>
                    </div>
                    <div>
                        <div style="margin-bottom: 12px;">
                            <strong>姓名：</strong>${userInfo.name || userInfo.username || '-'}
                        </div>
                        <div style="margin-bottom: 12px;">
                            <strong>工号：</strong>${userInfo.account || '-'}
                        </div>
                        <div style="margin-bottom: 12px;">
                            <strong>部门：</strong>${userInfo.department || '-'}
                        </div>
                        <div style="margin-bottom: 12px;">
                            <strong>科室：</strong>${userInfo.section || '-'}
                        </div>
                        <div style="margin-bottom: 12px;">
                            <strong>权限级别：</strong>${userInfo.level || '-'}
                        </div>
                        <div>
                            <strong>备品管理权限：</strong>${userInfo.splevel === 1 ? '管理员' : '普通用户'}
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 点击背景关闭弹窗
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeUserInfo();
                }
            });
        }

        // 关闭用户信息弹窗
        function closeUserInfo() {
            const modal = document.querySelector('.user-info-modal');
            if (modal) {
                modal.remove();
            }
        }

        // 登出函数
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('userInfo');
                window.location.href = 'login.html';
            }
        }

        // 错误处理
        function handleError(error, context) {
            console.error(`Error in ${context}:`, error);

            // 显示用户友好的错误信息
            const errorContent = `
                <div class="welcome-content" style="color: #ff4d4f;">
                    <h2>⚠️ 系统错误</h2>
                    <p style="margin-top: 16px;">抱歉，系统遇到了一个错误。</p>
                    <p style="margin-top: 8px; font-size: 14px; color: #666;">
                        错误信息：${error.message || '未知错误'}
                    </p>
                    <button onclick="location.reload()" style="
                        margin-top: 16px;
                        padding: 8px 16px;
                        background: #1890ff;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                    ">刷新页面</button>
                </div>
            `;

            document.getElementById('mainContent').innerHTML = errorContent;
        }

        // 检查依赖库是否加载成功
        function checkDependencies() {
            const dependencies = [
                { name: 'React', obj: window.React },
                { name: 'ReactDOM', obj: window.ReactDOM },
                { name: 'Ant Design', obj: window.antd },
                { name: 'Lodash', obj: window._ }
            ];

            const missing = dependencies.filter(dep => !dep.obj);

            if (missing.length > 0) {
                const missingNames = missing.map(dep => dep.name).join(', ');
                throw new Error(`缺少依赖库: ${missingNames}`);
            }
        }

        // 初始化应用
        function initApp() {
            try {
                // 检查依赖库
                checkDependencies();

                // 初始化用户信息
                initUserInfo();

                // 渲染菜单
                renderMenu();

                // 渲染标签页
                renderTabs();

                // 绑定事件监听器
                const menuTrigger = document.getElementById('menuTrigger');
                const mobileOverlay = document.getElementById('mobileOverlay');
                const userProfile = document.getElementById('userProfile');

                if (menuTrigger) {
                    menuTrigger.addEventListener('click', toggleMobileMenu);
                }

                if (mobileOverlay) {
                    mobileOverlay.addEventListener('click', closeMobileMenu);
                }

                if (userProfile) {
                    userProfile.addEventListener('click', showUserMenu);
                }

                // 窗口大小变化时的处理
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 768) {
                        closeMobileMenu();
                    }
                });

                // 键盘快捷键支持
                document.addEventListener('keydown', function(e) {
                    // ESC 键关闭移动端菜单
                    if (e.key === 'Escape') {
                        closeMobileMenu();
                        closeUserInfo();
                    }
                });

                console.log('应用初始化成功');

            } catch (error) {
                handleError(error, 'initApp');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initApp);

        // 全局函数，供HTML调用
        window.showUserInfo = showUserInfo;
        window.closeUserInfo = closeUserInfo;
        window.logout = logout;
    </script>
</body>
</html>