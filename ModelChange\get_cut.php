<?php
header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// 数据库配置信息
$serverName = "109.120.2.35";
$connectionOptions = array(
    "Database" => "BASEDATA",
    "Uid" => "eqplink",
    "PWD" => "eqplink",
    "TrustServerCertificate" => true,
    "CharacterSet" => "UTF-8"
);

try {
    $conn = sqlsrv_connect($serverName, $connectionOptions);
    
    if ($conn === false) {
        throw new Exception("Connection failed: " . print_r(sqlsrv_errors(), true));
    }

    $demandQuery  = "
    SELECT 
        rdate,
        CUT,
        SUM(DailyDemand) AS TotalDemand
    FROM (
        SELECT 
            dp.rdate,
            cm.CUT,
            dp.eqpid,
            MAX(CASE 
                WHEN LEFT(dp.eqpid, 2) = '3S' AND cm.ICCNT > 4 
                THEN 5 
                ELSE 3 
            END) AS DailyDemand
        FROM [BASEDATA].[dbo].[DPSPLAN] dp
        JOIN [BASEDATA].[dbo].[CUTMANAGER] cm 
        ON cm.PRODUCTID = dp.product
        WHERE dp.rdate <= DATEADD(MM, 3, GETDATE())
        AND dp.rdate >= DATEADD(DD, -4, GETDATE())
        AND dp.qty < 7000
        AND dp.version = (SELECT MAX(version) FROM [BASEDATA].[dbo].[DPSPLAN])
        GROUP BY dp.rdate, cm.CUT, dp.eqpid
    ) AS Sub
    GROUP BY rdate, CUT
    ORDER BY rdate;
    ";
    
    $result = sqlsrv_query($conn, $demandQuery);
    
    $demandData = [];
    while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
        $row['rdate'] = $row['rdate']->format('Y-m-d');
        $demandData[] = $row;
    }

    // $cutQuery = "SELECT CUT, CUTQTY FROM [BASEDATA].[dbo].[CUTMANAGER]";
    $cutQuery = "SELECT DISTINCT
    cm.CUT,
    cm.CUTQTY,
    ISNULL(stats.able_count, 0) AS able
FROM [BASEDATA].[dbo].[CUTMANAGER] cm
LEFT JOIN (
    SELECT 
        LEFT(CUTID, 4) AS cut_prefix,
        COUNT(*) AS able_count
    FROM [BASEDATA].[dbo].[cut_cutline]
    WHERE statue = 'Inventory'
    GROUP BY LEFT(CUTID, 4)
) stats ON LEFT(cm.CUT, 4) = stats.cut_prefix";
    $cutResult = sqlsrv_query($conn, $cutQuery);
    
    $cutInventory = [];
    $cutAble = [];
    while ($row = sqlsrv_fetch_array($cutResult, SQLSRV_FETCH_ASSOC)) {
        $cutInventory[$row['CUT']] = $row['CUTQTY'];
        $cutAble[$row['CUT']] = $row['able'];
    }

    echo json_encode([
        'success' => true,
        'demand' => $demandData,
        'inventory' => $cutInventory,
        'able' => $cutAble
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

if ($conn) {
    sqlsrv_close($conn);
}
