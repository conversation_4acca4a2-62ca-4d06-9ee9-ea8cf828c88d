<?php
// 引入数据库配置
require_once __DIR__ . '/../php/db_config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // 获取查询参数
    $checkId = isset($_GET['check_id']) ? trim($_GET['check_id']) : '';
    
    if (empty($checkId)) {
        throw new Exception('缺少必要的参数: check_id');
    }
    
    // 查询点检主表数据 - 使用新的表结构
    $masterSql = "SELECT * FROM MC_Check_List WHERE CHECK_ID = ?";
    $masterStmt = $conn->prepare($masterSql);
    $masterStmt->bind_param('i', $checkId);
    $masterStmt->execute();
    $masterResult = $masterStmt->get_result();

    if ($masterResult->num_rows === 0) {
        throw new Exception('未找到指定的点检记录');
    }

    $masterData = $masterResult->fetch_assoc();

    // 查询点检明细表数据 - 使用新的表结构
    $detailSql = "SELECT * FROM Check_List_Detail WHERE CHECK_ID = ? ORDER BY DETAIL_ID";
    $detailStmt = $conn->prepare($detailSql);
    $detailStmt->bind_param('i', $checkId);
    $detailStmt->execute();
    $detailResult = $detailStmt->get_result();
    
    $detailData = [];
    while ($row = $detailResult->fetch_assoc()) {
        $detailData[] = $row;
    }
    
    // 返回成功响应
    echo json_encode([
        'success' => true,
        'data' => [
            'master' => $masterData,
            'details' => $detailData
        ],
        'message' => '查询成功'
    ]);
    
} catch (Exception $e) {
    // 记录错误日志
    error_log("点检详情查询错误: " . $e->getMessage());
    
    // 返回错误响应
    echo json_encode([
        'success' => false,
        'data' => null,
        'message' => '查询失败: ' . $e->getMessage()
    ]);
} finally {
    // 关闭数据库连接
    if (isset($masterStmt)) {
        $masterStmt->close();
    }
    if (isset($detailStmt)) {
        $detailStmt->close();
    }
    if (isset($conn)) {
        $conn->close();
    }
}
?>
