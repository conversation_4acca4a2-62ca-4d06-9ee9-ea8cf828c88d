<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ant Design Vue 表格示例</title>
    
    <!-- Ant Design Vue 样式 -->
    <link rel="stylesheet" href="libs/antd.min.css">
    
    <!-- 自定义样式 -->
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            margin-bottom: 24px;
            text-align: center;
        }
        
        .header h1 {
            color: #1890ff;
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .header p {
            color: #666;
            margin: 8px 0 0 0;
            font-size: 16px;
        }
        
        .table-container {
            margin-top: 20px;
        }
        
        /* 性别标签样式 */
        .gender-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .gender-male {
            background-color: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }

        .gender-female {
            background-color: #fff0f6;
            color: #eb2f96;
            border: 1px solid #ffadd2;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                padding: 16px;
            }

            .header h1 {
                font-size: 24px;
            }

            .header p {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 页面标题 -->
            <div class="header">
                <h1>用户信息管理表格</h1>
                <p>基于 Ant Design Vue 2.x 构建的响应式数据表格</p>
            </div>
            
            <!-- 表格容器 -->
            <div class="table-container">
                <a-config-provider :locale="locale">
                    <a-table
                        :columns="columns"
                        :data-source="dataSource"
                        :pagination="pagination"
                        :scroll="{ x: 600 }"
                        row-key="id"
                        size="middle"
                    >
                    </a-table>
                </a-config-provider>
            </div>
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="libs/vue.global.prod.js"></script>
    <!-- Ant Design Vue -->
    <script src="libs/antd.min.js"></script>

    <script>
        const { createApp, ref, reactive } = Vue;

        // 配置 Ant Design Vue 中文语言包
        const zhCN = {
            locale: 'zh-cn',
            Pagination: {
                items_per_page: '条/页',
                jump_to: '跳至',
                jump_to_confirm: '确定',
                page: '页',
                prev_page: '上一页',
                next_page: '下一页',
                prev_5: '向前 5 页',
                next_5: '向后 5 页',
                prev_3: '向前 3 页',
                next_3: '向后 3 页',
                page_size: '页码'
            },
            Table: {
                filterTitle: '筛选',
                filterConfirm: '确定',
                filterReset: '重置',
                selectAll: '全选当页',
                selectInvert: '反选当页',
                sortTitle: '排序',
                expand: '展开行',
                collapse: '关闭行'
            },
            Select: {
                notFoundContent: '无匹配结果'
            }
        };

        createApp({
            setup() {
                // 表格列定义
                const columns = [
                    {
                        title: '姓名',
                        dataIndex: 'name',
                        key: 'name',
                        width: 120,
                        fixed: 'left'
                    },
                    {
                        title: '年龄',
                        dataIndex: 'age',
                        key: 'age',
                        width: 100,
                        sorter: (a, b) => a.age - b.age,
                        sortDirections: ['ascend', 'descend']
                    },
                    {
                        title: '性别',
                        dataIndex: 'gender',
                        key: 'gender',
                        width: 100,
                        customRender: ({ text }) => {
                            const className = text === '男' ? 'gender-male' : 'gender-female';
                            return `<span class="gender-tag ${className}">${text}</span>`;
                        }
                    },
                    {
                        title: '地址',
                        dataIndex: 'address',
                        key: 'address',
                        ellipsis: true
                    }
                ];

                // 示例数据（8条记录）
                const dataSource = ref([
                    {
                        id: 1,
                        name: '张三',
                        age: 28,
                        gender: '男',
                        address: '北京市朝阳区建国门外大街1号'
                    },
                    {
                        id: 2,
                        name: '李四',
                        age: 32,
                        gender: '女',
                        address: '上海市浦东新区陆家嘴环路1000号'
                    },
                    {
                        id: 3,
                        name: '王五',
                        age: 25,
                        gender: '男',
                        address: '广州市天河区珠江新城花城大道85号'
                    },
                    {
                        id: 4,
                        name: '赵六',
                        age: 35,
                        gender: '女',
                        address: '深圳市南山区科技园南区深南大道9988号'
                    },
                    {
                        id: 5,
                        name: '钱七',
                        age: 29,
                        gender: '男',
                        address: '杭州市西湖区文三路259号'
                    },
                    {
                        id: 6,
                        name: '孙八',
                        age: 31,
                        gender: '女',
                        address: '成都市高新区天府大道中段1号'
                    },
                    {
                        id: 7,
                        name: '周九',
                        age: 27,
                        gender: '男',
                        address: '武汉市江汉区建设大道568号'
                    },
                    {
                        id: 8,
                        name: '吴十',
                        age: 33,
                        gender: '女',
                        address: '南京市鼓楼区中山路1号'
                    }
                ]);

                // 分页配置
                const pagination = reactive({
                    current: 1,
                    pageSize: 5,
                    total: dataSource.value.length,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                    pageSizeOptions: ['5', '10', '20', '50']
                });

                // 中文语言包配置
                const locale = ref(zhCN);

                return {
                    columns,
                    dataSource,
                    pagination,
                    locale
                };
            }
        }).use(antd).mount('#app');
    </script>
</body>
</html>
