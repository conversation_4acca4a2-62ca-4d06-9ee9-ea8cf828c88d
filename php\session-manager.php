<?php
class SessionManager {
    private $conn;
    private $sessionTimeout = 28800; // 8小时
    
    public function __construct($connection) {
        $this->conn = $connection;
        $this->createSessionTable();
    }
    
    // 创建会话表
    private function createSessionTable() {
        $sql = "CREATE TABLE IF NOT EXISTS user_sessions (
            session_id VARCHAR(64) PRIMARY KEY,
            user_account VARCHAR(50) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            ip_address VARCHAR(45),
            user_agent TEXT,
            is_active TINYINT(1) DEFAULT 1
        )";
        $this->conn->query($sql);
    }
    
    // 创建新会话
    public function createSession($userAccount, $ipAddress, $userAgent) {
        $sessionId = bin2hex(random_bytes(32));
        
        $sql = "INSERT INTO user_sessions (session_id, user_account, ip_address, user_agent) 
                VALUES (?, ?, ?, ?)";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("ssss", $sessionId, $userAccount, $ipAddress, $userAgent);
        
        if ($stmt->execute()) {
            return $sessionId;
        }
        return false;
    }
    
    // 验证会话
    public function validateSession($sessionId, $userAccount) {
        $sql = "SELECT * FROM user_sessions 
                WHERE session_id = ? AND user_account = ? AND is_active = 1 
                AND last_activity > DATE_SUB(NOW(), INTERVAL ? SECOND)";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("ssi", $sessionId, $userAccount, $this->sessionTimeout);
        $stmt->execute();
        
        return $stmt->get_result()->num_rows > 0;
    }
}
?>