<?php
// 引入数据库配置
require_once __DIR__ . '/../php/db_config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // 获取查询参数
    $line = isset($_GET['line']) ? trim($_GET['line']) : '';
    $model = isset($_GET['model']) ? trim($_GET['model']) : '';
    $jig = isset($_GET['jig']) ? trim($_GET['jig']) : '';
    
    // 构建SQL查询
    $sql = "SELECT LINE, MODEL, JIG FROM jig WHERE 1=1";
    $params = [];
    $types = "";
    
    // 添加查询条件（支持模糊搜索）
    if (!empty($line)) {
        $sql .= " AND LINE LIKE ?";
        $params[] = "%$line%";
        $types .= "s";
    }
    
    if (!empty($model)) {
        $sql .= " AND MODEL LIKE ?";
        $params[] = "%$model%";
        $types .= "s";
    }
    
    if (!empty($jig)) {
        $sql .= " AND JIG LIKE ?";
        $params[] = "%$jig%";
        $types .= "s";
    }
    
    // 添加排序
    $sql .= " ORDER BY LINE, MODEL, JIG";
    
    // 准备和执行查询
    $stmt = $conn->prepare($sql);
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
    
    // 返回成功响应
    echo json_encode([
        'success' => true,
        'data' => $data,
        'count' => count($data),
        'message' => count($data) > 0 ? '查询成功' : '未找到匹配的数据'
    ]);
    
} catch (Exception $e) {
    // 记录错误日志
    error_log("JIG查询错误: " . $e->getMessage());
    
    // 返回错误响应
    echo json_encode([
        'success' => false,
        'data' => [],
        'count' => 0,
        'message' => '查询失败: ' . $e->getMessage()
    ]);
} finally {
    // 关闭数据库连接
    if (isset($stmt)) {
        $stmt->close();
    }
    if (isset($conn)) {
        $conn->close();
    }
}
?>
