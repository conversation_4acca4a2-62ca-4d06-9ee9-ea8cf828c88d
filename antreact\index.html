<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ant Design 表格示例</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@4.24.12/dist/antd.min.css">
</head>
<body>
    <div id="root"></div>

    <script src="https://cdn.jsdelivr.net/npm/react@16.14.0/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@16.14.0/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/antd@4.24.12/dist/antd.min.js"></script>

    <script>
        const { Table } = antd;

        // 硬编码的示例数据
        const dataSource = [
            {
                key: '1',
                name: '张三',
                age: 32,
                gender: '男',
                address: '北京市朝阳区建国门外大街1号'
            },
            {
                key: '2',
                name: '李四',
                age: 28,
                gender: '女',
                address: '上海市浦东新区陆家嘴环路1000号'
            },
            {
                key: '3',
                name: '王五',
                age: 45,
                gender: '男',
                address: '广州市天河区珠江新城华夏路1号'
            },
            {
                key: '4',
                name: '赵六',
                age: 23,
                gender: '女',
                address: '深圳市南山区科技园南区深南大道2008号'
            },
            {
                key: '5',
                name: '钱七',
                age: 36,
                gender: '男',
                address: '杭州市西湖区文三路259号'
            },
            {
                key: '6',
                name: '孙八',
                age: 29,
                gender: '女',
                address: '成都市高新区天府大道中段1号'
            },
            {
                key: '7',
                name: '周九',
                age: 41,
                gender: '男',
                address: '武汉市江汉区中山大道818号'
            },
            {
                key: '8',
                name: '吴十',
                age: 31,
                gender: '女',
                address: '南京市鼓楼区中山路1号'
            }
        ];

        // 表格列定义，年龄列包含排序功能
        const columns = [
            {
                title: '姓名',
                dataIndex: 'name',
                key: 'name',
                width: 120
            },
            {
                title: '年龄',
                dataIndex: 'age',
                key: 'age',
                width: 100,
                sorter: (a, b) => a.age - b.age,
                sortDirections: ['ascend', 'descend']
            },
            {
                title: '性别',
                dataIndex: 'gender',
                key: 'gender',
                width: 80
            },
            {
                title: '地址',
                dataIndex: 'address',
                key: 'address'
            }
        ];

        function App() {
            const handleTableChange = (pagination, filters, sorter) => {
                console.log('排序信息:', sorter);
            };

            return React.createElement(
                'div',
                { 
                    style: { 
                        padding: '20px',
                        maxWidth: '1200px',
                        margin: '0 auto'
                    } 
                },
                React.createElement(
                    'h1',
                    { style: { textAlign: 'center', marginBottom: '20px' } },
                    'Ant Design 表格示例'
                ),
                React.createElement(Table, {
                    dataSource: dataSource,
                    columns: columns,
                    onChange: handleTableChange,
                    pagination: {
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                    }
                })
            );
        }

        ReactDOM.render(
            React.createElement(App, null),
            document.getElementById('root')
        );
    </script>
</body>
</html>