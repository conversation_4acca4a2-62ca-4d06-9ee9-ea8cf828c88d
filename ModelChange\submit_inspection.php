<?php
// 引入数据库配置
require_once __DIR__ . '/../php/db_config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => '只允许POST请求'
    ]);
    exit;
}

try {
    // 获取POST数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('无效的JSON数据');
    }
    
    // 验证必填字段 - 移除BEFORE_PRODUCT字段
    $requiredFields = ['SITEID', 'EQPID', 'MC_DATE', 'OLD_PRODUCT', 'TRACK_PRODUCT', 'PROJECT', 'MC_OPERATOR', 'INSPECTOR'];
    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            throw new Exception("缺少必填字段: $field");
        }
    }
    
    // 开始事务
    $conn->autocommit(false);

    // 插入点检主表 - 使用新的表结构
    $masterSql = "INSERT INTO MC_Check_List
                  (SITEID, EQPID, MC_DATE, OLD_PRODUCT, TRACK_PRODUCT, PROJECT, MC_OPERATOR, INSPECTOR)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

    $masterStmt = $conn->prepare($masterSql);
    $masterStmt->bind_param('ssssssss',
        $data['SITEID'],
        $data['EQPID'],
        $data['MC_DATE'],
        $data['OLD_PRODUCT'],
        $data['TRACK_PRODUCT'],
        $data['PROJECT'],
        $data['MC_OPERATOR'],
        $data['INSPECTOR']
    );
    
    if (!$masterStmt->execute()) {
        throw new Exception('插入点检主表失败: ' . $masterStmt->error);
    }

    // 获取插入的CHECK_ID
    $checkId = $conn->insert_id;

    // 插入点检明细表 - 使用新的表结构
    if (isset($data['details']) && is_array($data['details'])) {
        $detailSql = "INSERT INTO Check_List_Detail
                      (CHECK_ID, ITEM_NO, CHECK_CONTENT, METHOD, SPEC_LSL, SPEC_TAR, SPEC_USL,
                       PIC_OK, PIC_NG, MEASURE, JUDGEMENT)
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $detailStmt = $conn->prepare($detailSql);

        foreach ($data['details'] as $detail) {
            // 跳过空行
            if (empty($detail['ITEM_NO']) && empty($detail['CHECK_CONTENT'])) {
                continue;
            }

            $detailStmt->bind_param('issssssssss',
                $checkId,
                $detail['ITEM_NO'] ?? '',
                $detail['CHECK_CONTENT'] ?? '',
                $detail['METHOD'] ?? '',
                $detail['SPEC_LSL'] ?? '',
                $detail['SPEC_TAR'] ?? '',
                $detail['SPEC_USL'] ?? '',
                $detail['PIC_OK'] ?? '',
                $detail['PIC_NG'] ?? '',
                $detail['MEASURE'] ?? '',
                $detail['JUDGEMENT'] ?? ''
            );

            if (!$detailStmt->execute()) {
                throw new Exception('插入点检明细表失败: ' . $detailStmt->error);
            }
        }
    }
    
    // 提交事务
    $conn->commit();
    
    // 返回成功响应
    echo json_encode([
        'success' => true,
        'data' => [
            'check_id' => $checkId
        ],
        'message' => '点检数据提交成功'
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    if (isset($conn)) {
        $conn->rollback();
    }
    
    // 记录错误日志
    error_log("点检数据提交错误: " . $e->getMessage());
    
    // 返回错误响应
    echo json_encode([
        'success' => false,
        'data' => null,
        'message' => '提交失败: ' . $e->getMessage()
    ]);
} finally {
    // 恢复自动提交
    if (isset($conn)) {
        $conn->autocommit(true);
    }
    
    // 关闭数据库连接
    if (isset($masterStmt)) {
        $masterStmt->close();
    }
    if (isset($detailStmt)) {
        $detailStmt->close();
    }
    if (isset($conn)) {
        $conn->close();
    }
}
?>
