点检主表录入：
SITEID修改为下拉选项框，选项加载[bigdata].[dbo].[Model_Change]的[site_id]所有唯一值；
EQPID修改为下拉选项框，选项默认加载[bigdata].[dbo].[Model_Change]的[eqp_id]所有唯一值，根据SITEID选项进行筛选；
OLD_PRODUCT、TRACK_PRODUCT、MC_OPERATOR、INSPECTOR移除输入框，直接修改为可编辑的单元格(contenteditable="true")；
可编辑表格边框样式淡蓝色边框（
    border-color: #1890ff;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    background-color: #fff;）；
同步修改确认JavaScript收集主表数据功能正确运行；



根据get_cut_details.php中sqlserver连接方式，将submit_inspection.php中连接方式从mysql修改为sqlserver

MC_Check_List表sqlserver信息：$serverName = "************"; 
$connectionOptions = array( "Database" => EQP_management, "Uid" => "eqplink", "PWD" => "eqplink", "TrustServerCertificate" => true, "CharacterSet" => "UTF-8" );
表名MC_Check_List改为[EQP_management].[dbo].[MC_Check_List]，

Check_List_Detail表sqlserver信息：$serverName = "************"; 
$connectionOptions = array( "Database" => EQP_management, "Uid" => "eqplink", "PWD" => "eqplink", "TrustServerCertificate" => true, "CharacterSet" => "UTF-8" );
表名Check_List_Detail改为[EQP_management].[dbo].[Check_List_Detail]，
字段名称、查询逻辑、返回结果保持不变




-- 2. 创建新的切机履历表 Model_Change
CREATE TABLE Model_Change (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    site_id VARCHAR(50) NOT NULL COMMENT '站点ID',
    eqp_id VARCHAR(50) NOT NULL COMMENT '设备ID',
    date DATE NOT NULL COMMENT '日期',
    shift1 VARCHAR(50) NOT NULL COMMENT '班次1',
    shift2 VARCHAR(50) NOT NULL COMMENT '班次2',
    mc_time DATE NOT NULL COMMENT '切机时间',
    mc_times VARCHAR(50) NOT NULL COMMENT '切机次数',
    start_time1 DATE NOT NULL COMMENT '开始时间1',
    start_time2 DATE NOT NULL COMMENT '开始时间2',
    s_time VARCHAR(50) NOT NULL COMMENT 'S时间',
    mc_type VARCHAR(50) NOT NULL COMMENT '切机类型',
    noplan VARCHAR(50) NOT NULL COMMENT '无计划',
    old_model VARCHAR(50) NOT NULL COMMENT '旧型号',
    track_product VARCHAR(50) NOT NULL COMMENT '跟踪产品',
    abaflag VARCHAR(50) NOT NULL COMMENT 'ABA标志',
    prod_type VARCHAR(50) NOT NULL COMMENT '产品类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_eqp_date (eqp_id, date),
    INDEX idx_date (date),
    INDEX idx_old_model (old_model),
    INDEX idx_track_product (track_product)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='切机履历表';

-- 3. 创建新的点检主表 MC_Check_List
CREATE TABLE MC_Check_List (
    CHECK_ID INT AUTO_INCREMENT PRIMARY KEY COMMENT '点检主键ID',
    SITEID VARCHAR(50) NOT NULL COMMENT '站点ID',
    EQPID VARCHAR(50) NOT NULL COMMENT '设备ID',
    MC_DATE DATE NOT NULL COMMENT '切机日期',
    OLD_PRODUCT VARCHAR(100) NULL COMMENT '旧产品',
    TRACK_PRODUCT VARCHAR(100) NULL COMMENT '跟踪产品',
    PROJECT VARCHAR(20) NOT NULL COMMENT '项目',
    MC_OPERATOR VARCHAR(20) NOT NULL COMMENT '切机操作员',
    INSPECTOR VARCHAR(50) NOT NULL COMMENT '点检员',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_siteid_eqpid (SITEID, EQPID),
    INDEX idx_mc_date (MC_DATE),
    INDEX idx_old_product (OLD_PRODUCT),
    INDEX idx_track_product (TRACK_PRODUCT),
    INDEX idx_project (PROJECT)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='点检主表';

-- 4. 创建新的点检明细表 Check_List_Detail
CREATE TABLE Check_List_Detail (
    DETAIL_ID INT AUTO_INCREMENT PRIMARY KEY COMMENT '明细主键ID',
    CHECK_ID INT NOT NULL COMMENT '点检主表ID',
    ITEM_NO VARCHAR(20) NULL COMMENT '项目编号',
    CHECK_CONTENT VARCHAR(50) NULL COMMENT '检查内容',
    METHOD VARCHAR(50) NULL COMMENT '检查方法',
    SPEC_LSL VARCHAR(50) NULL COMMENT '规格下限',
    SPEC_TAR VARCHAR(50) NULL COMMENT '规格目标值',
    SPEC_USL VARCHAR(50) NULL COMMENT '规格上限',
    PIC_OK VARCHAR(50) NULL COMMENT '合格图片',
    PIC_NG VARCHAR(20) NULL COMMENT '不合格图片',
    MEASURE VARCHAR(50) NULL COMMENT '测量值',
    JUDGEMENT VARCHAR(20) NULL COMMENT '判定结果',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (CHECK_ID) REFERENCES MC_Check_List(CHECK_ID) ON DELETE CASCADE ON UPDATE CASCADE,
    INDEX idx_check_id (CHECK_ID),
    INDEX idx_item_no (ITEM_NO),
    INDEX idx_judgement (JUDGEMENT)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='点检明细表';

-- 5. 插入测试数据
-- 插入切机履历测试数据
INSERT INTO Model_Change (eqp_id, date, shift1, shift2, mc_time, mc_times, start_time1, start_time2, s_time, mc_type, noplan, old_model, track_product, abaflag, prod_type) VALUES
('EQP001', '2024-01-15', 'A班', 'B班', '2024-01-15', '1', '2024-01-15', '2024-01-15', '08:00', 'Normal', 'No', 'MODEL_A', 'TRACK_001', 'ABA1', 'TYPE1'),
('EQP002', '2024-01-16', 'B班', 'C班', '2024-01-16', '2', '2024-01-16', '2024-01-16', '16:00', 'Emergency', 'Yes', 'MODEL_B', 'TRACK_002', 'ABA2', 'TYPE2'),
('EQP003', '2024-01-17', 'C班', 'A班', '2024-01-17', '1', '2024-01-17', '2024-01-17', '00:00', 'Normal', 'No', 'MODEL_C', 'TRACK_003', 'ABA3', 'TYPE1');

-- 插入点检主表测试数据
INSERT INTO MC_Check_List (SITEID, EQPID, MC_DATE, OLD_PRODUCT, TRACK_PRODUCT, PROJECT, MC_OPERATOR, INSPECTOR) VALUES
('SITE001', 'EQP001', '2024-01-15', 'MODEL_A', 'TRACK_001', 'CP', 'OP001', 'INSP001'),
('SITE002', 'EQP002', '2024-01-16', 'MODEL_B', 'TRACK_002', 'OLB', 'OP002', 'INSP002'),
('SITE003', 'EQP003', '2024-01-17', 'MODEL_C', 'TRACK_003', 'CP', 'OP003', 'INSP003');

-- 插入点检明细表测试数据
INSERT INTO Check_List_Detail (CHECK_ID, ITEM_NO, CHECK_CONTENT, METHOD, SPEC_LSL, SPEC_TAR, SPEC_USL, PIC_OK, PIC_NG, MEASURE, JUDGEMENT) VALUES
(1, 'ITEM001', '尺寸检查', '卡尺测量', '9.8', '10.0', '10.2', 'ok1.jpg', 'ng1.jpg', '10.1', 'OK'),
(1, 'ITEM002', '外观检查', '目视检查', '', '良好', '', 'ok2.jpg', '', '良好', 'OK'),
(2, 'ITEM001', '尺寸检查', '卡尺测量', '9.8', '10.0', '10.2', 'ok3.jpg', 'ng2.jpg', '9.9', 'OK'),
(2, 'ITEM002', '外观检查', '目视检查', '', '良好', '', 'ok4.jpg', '', '良好', 'OK'),
(2, 'ITEM003', '功能检查', '测试仪检查', '95', '100', '105', 'ok5.jpg', '', '98', 'OK'),
(3, 'ITEM001', '尺寸检查', '卡尺测量', '9.8', '10.0', '10.2', 'ok6.jpg', '', '10.0', 'OK'),
(3, 'ITEM002', '外观检查', '目视检查', '', '良好', '', 'ok7.jpg', '', '良好', 'OK');



CREATE TABLE MC_Check_List (
    CHECK_ID INT IDENTITY(1,1) PRIMARY KEY,
    SITEID VARCHAR(50) NOT NULL,
    EQPID VARCHAR(50) NOT NULL,
    MC_DATE DATETIME NOT NULL,
    OLD_PRODUCT VARCHAR(100) NULL,
    TRACK_PRODUCT VARCHAR(100) NULL,
    PROJECT VARCHAR(20) NOT NULL,
    MC_OPERATOR VARCHAR(20) NOT NULL,
    INSPECTOR VARCHAR(50) NOT NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

CREATE INDEX idx_siteid_eqpid ON MC_Check_List (SITEID, EQPID);
CREATE INDEX idx_mc_date ON MC_Check_List (MC_DATE);
CREATE INDEX idx_old_product ON MC_Check_List (OLD_PRODUCT);
CREATE INDEX idx_track_product ON MC_Check_List (TRACK_PRODUCT);
CREATE INDEX idx_project ON MC_Check_List (PROJECT);

CREATE TABLE Check_List_Detail (
    DETAIL_ID INT IDENTITY(1,1) PRIMARY KEY,
    CHECK_ID INT NOT NULL,
    ITEM_NO VARCHAR(20) NULL,
    CHECK_CONTENT VARCHAR(50) NULL,
    METHOD VARCHAR(50) NULL,
    SPEC_LSL VARCHAR(50) NULL,
    SPEC_TAR VARCHAR(50) NULL,
    SPEC_USL VARCHAR(50) NULL,
    PIC_OK VARCHAR(50) NULL,
    PIC_NG VARCHAR(20) NULL,
    MEASURE VARCHAR(50) NULL,
    JUDGEMENT VARCHAR(20) NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_Check_List_Detail_CHECK_ID FOREIGN KEY (CHECK_ID) REFERENCES MC_Check_List(CHECK_ID) ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE NONCLUSTERED INDEX idx_check_id ON Check_List_Detail (CHECK_ID);
CREATE NONCLUSTERED INDEX idx_item_no ON Check_List_Detail (ITEM_NO);
CREATE NONCLUSTERED INDEX idx_judgement ON Check_List_Detail (JUDGEMENT);