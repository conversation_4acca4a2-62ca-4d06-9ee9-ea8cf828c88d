<?php
header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

$serverName = "109.120.2.35";
$connectionOptions = array(
    "Database" => "BASEDATA",
    "Uid" => "eqplink",
    "PWD" => "eqplink",
    "TrustServerCertificate" => true,
    "CharacterSet" => "UTF-8"
);

try {
    $conn = sqlsrv_connect($serverName, $connectionOptions);

    if ($conn === false) {
        throw new Exception("Connection failed: " . print_r(sqlsrv_errors(), true));
    }

    $cut = isset($_GET['cut']) ? substr($_GET['cut'], 0, 4) : '';

    if (empty($cut)) {
        throw new Exception("缺少必要的参数");
    }

    $query = "SELECT [CUTID], [CUTLINE], [statue], [smod] 
              FROM [BASEDATA].[dbo].[cut_cutline] 
              WHERE [CUTID] LIKE ?";

    $params = array($cut . '%');
    $result = sqlsrv_query($conn, $query, $params);

    if ($result === false) {
        throw new Exception("查询失败: " . print_r(sqlsrv_errors(), true));
    }

    $data = array();
    while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
        $data[] = $row;
    }

    echo json_encode([
        'success' => true,
        'data' => $data
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

if (isset($conn) && $conn) {
    sqlsrv_close($conn);
}
