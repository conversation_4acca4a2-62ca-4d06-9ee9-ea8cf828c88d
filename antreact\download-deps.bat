@echo off
echo Downloading dependencies...

echo Downloading React...
curl -o "libs\react.production.min.js" "https://cdn.jsdelivr.net/npm/react@16.14.0/umd/react.production.min.js"

echo Downloading ReactDOM...
curl -o "libs\react-dom.production.min.js" "https://cdn.jsdelivr.net/npm/react-dom@16.14.0/umd/react-dom.production.min.js"

echo Downloading Ant Design JS...
curl -o "libs\antd.min.js" "https://cdn.jsdelivr.net/npm/antd@4.24.12/dist/antd.min.js"

echo Downloading Ant Design CSS...
curl -o "libs\antd.min.css" "https://cdn.jsdelivr.net/npm/antd@4.24.12/dist/antd.min.css"

echo Download completed!
echo You can now open index-local.html to view the local version.
pause