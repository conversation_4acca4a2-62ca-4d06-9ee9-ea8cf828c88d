<?php
// 引入数据库配置
require_once __DIR__ . '/../php/db_config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // 获取SITE选项 - 从切机履历表获取
    $siteQuery = "SELECT DISTINCT site_id FROM Model_Change ORDER BY site_id";
    $siteResult = $conn->query($siteQuery);

    $sites = [];
    if ($siteResult) {
        while ($row = $siteResult->fetch_assoc()) {
            $sites[] = $row['site_id'];
        }
    }

    // 获取EQP选项 - 从切机履历表获取
    $eqpQuery = "SELECT DISTINCT eqp_id as EQPID FROM Model_Change ORDER BY eqp_id";
    $eqpResult = $conn->query($eqpQuery);

    $eqps = [];
    if ($eqpResult) {
        while ($row = $eqpResult->fetch_assoc()) {
            $eqps[] = $row['EQPID'];
        }
    }
    
    // 返回成功响应
    echo json_encode([
        'success' => true,
        'data' => [
            'sites' => $sites,
            'eqps' => $eqps
        ],
        'message' => '获取选项成功'
    ]);
    
} catch (Exception $e) {
    // 记录错误日志
    error_log("获取选项错误: " . $e->getMessage());
    
    // 返回错误响应
    echo json_encode([
        'success' => false,
        'data' => [
            'sites' => [],
            'eqps' => []
        ],
        'message' => '获取选项失败: ' . $e->getMessage()
    ]);
} finally {
    // 关闭数据库连接
    if (isset($conn)) {
        $conn->close();
    }
}
?>
