# Ant Design 表格示例

这是一个基于 Ant Design 4.x 版本的 React 表格组件示例。

## 功能特性

- ✅ 基于 Ant Design 4.x 版本
- ✅ 包含姓名、年龄、性别、地址四列数据
- ✅ 年龄列支持升序/降序排序
- ✅ 内置硬编码示例数据（8条记录）
- ✅ 支持分页功能
- ✅ 响应式设计

## 文件结构

```
anttest/
├── index.html              # 主页面文件（使用 CDN 依赖）
├── index-local.html        # 本地版本（需要下载依赖文件）
├── README.md              # 说明文档
└── libs/                  # 本地依赖文件夹（需要手动下载）
    ├── react.production.min.js
    ├── react-dom.production.min.js
    ├── antd.min.js
    └── antd.min.css
```

## 使用方法

### 方法一：直接使用 CDN 版本
直接打开 `index.html` 文件即可在浏览器中查看效果。该版本依赖 CDN 资源，需要网络连接。

### 方法二：使用本地依赖版本
1. 运行 `download-deps.bat` 自动下载依赖文件（需要网络连接）
2. 如果自动下载失败，请手动下载以下文件到 `libs` 文件夹中：
   - react.production.min.js: https://cdn.jsdelivr.net/npm/react@16.14.0/umd/react.production.min.js
   - react-dom.production.min.js: https://cdn.jsdelivr.net/npm/react-dom@16.14.0/umd/react-dom.production.min.js
   - antd.min.js: https://cdn.jsdelivr.net/npm/antd@4.24.12/dist/antd.min.js
   - antd.min.css: https://cdn.jsdelivr.net/npm/antd@4.24.12/dist/antd.min.css
3. 下载完成后打开 `index-local.html` 文件

**注意：** 由于网络连接问题，当前自动下载失败。建议先使用 `index.html`（CDN版本）测试功能。

## 表格功能说明

- **姓名列**：显示人员姓名
- **年龄列**：显示年龄，支持点击表头进行升序/降序排序
- **性别列**：显示性别信息
- **地址列**：显示详细地址信息

## 示例数据

页面包含8条硬编码的示例数据，涵盖不同年龄段和地区的人员信息。

## 技术栈

- React 16.14.0
- Ant Design 4.24.12
- 纯 HTML + JavaScript（无需构建工具）

## 浏览器兼容性

支持所有现代浏览器，包括：
- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+