<?php
// 引入数据库配置
require_once __DIR__ . '/../php/db_config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // 获取查询参数
    $siteid = isset($_GET['siteid']) ? trim($_GET['siteid']) : '';
    $eqpid = isset($_GET['eqpid']) ? trim($_GET['eqpid']) : '';
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $pageSize = isset($_GET['page_size']) ? intval($_GET['page_size']) : 10;
    
    // 构建查询条件
    $whereConditions = [];
    $params = [];
    $types = '';
    
    if (!empty($siteid)) {
        $whereConditions[] = "mch.site_id = ?";
        $params[] = $siteid;
        $types .= 's';
    }

    if (!empty($eqpid)) {
        $whereConditions[] = "mch.eqp_id = ?";
        $params[] = $eqpid;
        $types .= 's';
    }
    
    $whereClause = '';
    if (!empty($whereConditions)) {
        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
    }
    
    // 查询总记录数
    $countSql = "SELECT COUNT(*) as total FROM Model_Change mch $whereClause";
    
    if (!empty($params)) {
        $countStmt = $conn->prepare($countSql);
        $countStmt->bind_param($types, ...$params);
        $countStmt->execute();
        $totalResult = $countStmt->get_result();
    } else {
        $totalResult = $conn->query($countSql);
    }
    
    $totalRow = $totalResult->fetch_assoc();
    $total = $totalRow['total'];
    
    // 构建主查询
    $offset = ($page - 1) * $pageSize;
    $sql = "SELECT
                mch.*,
                GROUP_CONCAT(DISTINCT mcl.PROJECT SEPARATOR ', ') as inspection_projects
            FROM Model_Change mch
            LEFT JOIN MC_Check_List mcl ON (
                mch.eqp_id = mcl.EQPID
                AND mch.date = mcl.MC_DATE
                AND mch.old_model = mcl.OLD_PRODUCT
                AND mch.track_product = mcl.TRACK_PRODUCT
            )
            $whereClause
            GROUP BY mch.id
            ORDER BY mch.date DESC
            LIMIT ?, ?";
    
    // 准备参数
    $finalParams = array_merge($params, [$offset, $pageSize]);
    $finalTypes = $types . 'ii';
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($finalTypes, ...$finalParams);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $historyData = [];
    while ($row = $result->fetch_assoc()) {
        // 查询关联的点检记录详情
        $inspectionDetails = [];
        if (!empty($row['inspection_projects'])) {
            $inspectionSql = "SELECT CHECK_ID as check_id, PROJECT, INSPECTOR as inspector
                             FROM MC_Check_List
                             WHERE EQPID = ? AND MC_DATE = ?
                             AND OLD_PRODUCT = ? AND TRACK_PRODUCT = ?";
            $inspectionStmt = $conn->prepare($inspectionSql);
            $inspectionStmt->bind_param('ssss',
                $row['eqp_id'],
                $row['date'],
                $row['old_model'],
                $row['track_product']
            );
            $inspectionStmt->execute();
            $inspectionResult = $inspectionStmt->get_result();

            while ($inspectionRow = $inspectionResult->fetch_assoc()) {
                $inspectionDetails[] = $inspectionRow;
            }
        }
        
        $row['inspection_details'] = $inspectionDetails;
        $historyData[] = $row;
    }
    
    // 计算总页数
    $totalPages = ceil($total / $pageSize);
    
    // 返回成功响应
    echo json_encode([
        'success' => true,
        'data' => $historyData,
        'pagination' => [
            'total' => $total,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'page_size' => $pageSize
        ],
        'message' => '查询成功'
    ]);
    
} catch (Exception $e) {
    // 记录错误日志
    error_log("切机履历查询错误: " . $e->getMessage());
    
    // 返回错误响应
    echo json_encode([
        'success' => false,
        'data' => [],
        'pagination' => [
            'total' => 0,
            'current_page' => 1,
            'total_pages' => 0,
            'page_size' => 10
        ],
        'message' => '查询失败: ' . $e->getMessage()
    ]);
} finally {
    // 关闭数据库连接
    if (isset($stmt)) {
        $stmt->close();
    }
    if (isset($conn)) {
        $conn->close();
    }
}
?>
